import torch
from pathlib import Path
from dataset.dezh import TranslationDataset

# 配置工作目录和设备
base_dir = "./train_process/transformer-dezh"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/transformer_checkpoints")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
data_dir = "data/de-zh"

# 初始化数据集
dataset = TranslationDataset(data_dir)
max_seq_length = 42  # 句子最大长度


def translate(src: str) -> str:
    """
    使用训练好的Transformer模型进行德中翻译
    :param src: 德语句子，如"Ich liebe maschinelles Lernen."
    :return: 翻译后的中文句子，如"我喜欢机器学习"
    """
    # 加载模型
    model = torch.load(model_dir / 'best.pt', map_location=device)
    model.to(device)
    model.eval()

    # 预处理输入句子
    src_tokens = [0] + dataset.de_vocab(dataset.de_tokenizer(src)) + [1]  # 使用德语分词器和词汇表
    src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(device)  # shape: [1, seq_len]

    # 初始化目标序列（以<bos>开头）
    tgt_tensor = torch.tensor([[0]]).to(device)  # shape: [1, 1]

    # 自回归生成翻译结果
    with torch.no_grad():
        for _ in range(max_seq_length):
            out = model(src_tensor, tgt_tensor)  # transformer计算
            predict = model.predictor(out[:, -1])  # 只取最后一个词的预测
            next_token = torch.argmax(predict, dim=1)  # 选择概率最高的词

            # 将新词添加到目标序列
            tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)

            # 遇到<eos>（索引1）则停止生成
            if next_token.item() == 1:
                break

    # 将token索引转换为中文句子
    tgt_tokens = tgt_tensor.squeeze().tolist()
    translated = ' '.join(dataset.zh_vocab.lookup_tokens(tgt_tokens))

    # 清理特殊标记并返回结果
    return translated.replace("<s>", "").replace("</s>", "").strip()


# 测试翻译
if __name__ == '__main__':
    # 使用德语圣经句子进行测试
    print(translate("Am Anfang schuf Gott Himmel und Erde."))  # 创世纪 1:1
    print(translate("Und Gott sprach: Es werde Licht! Und es ward Licht."))  # 创世纪 1:3
    print(translate("Denn also hat Gott die Welt geliebt, dass er seinen eingeborenen Sohn gab."))  # 约翰福音 3:16
